name: bmad-agentic-microservices
version: 1.0.0
description: Agentic AI-Powered Microservices & Micro-Frontend Architecture expansion pack for BMAD Method - Complete framework for building large-scale distributed applications with embedded multi-agent systems
author: BMAD Team
category: architecture
tags:
  - microservices
  - micro-frontend
  - agentic-ai
  - multi-agent
  - distributed-systems
  - event-driven
  - orchestration

files:
  # Specialized Agents
  - source: agents/system-architect.md
    destination: .bmad-core/agents/system-architect.md
  - source: agents/microservice-architect.md
    destination: .bmad-core/agents/microservice-architect.md
  - source: agents/agentic-ai-designer.md
    destination: .bmad-core/agents/agentic-ai-designer.md
  - source: agents/micro-frontend-architect.md
    destination: .bmad-core/agents/micro-frontend-architect.md
  - source: agents/service-orchestrator.md
    destination: .bmad-core/agents/service-orchestrator.md
  - source: agents/agent-workflow-designer.md
    destination: .bmad-core/agents/agent-workflow-designer.md

  # System Level Templates
  - source: templates/system-project-brief-tmpl.md
    destination: .bmad-core/templates/system-project-brief-tmpl.md
  - source: templates/system-prd-tmpl.md
    destination: .bmad-core/templates/system-prd-tmpl.md
  - source: templates/system-architecture-tmpl.md
    destination: .bmad-core/templates/system-architecture-tmpl.md
  - source: templates/agentic-system-design-tmpl.md
    destination: .bmad-core/templates/agentic-system-design-tmpl.md

  # Microservice Level Templates
  - source: templates/microservice-brief-tmpl.md
    destination: .bmad-core/templates/microservice-brief-tmpl.md
  - source: templates/microservice-prd-tmpl.md
    destination: .bmad-core/templates/microservice-prd-tmpl.md
  - source: templates/microservice-architecture-tmpl.md
    destination: .bmad-core/templates/microservice-architecture-tmpl.md
  - source: templates/agent-integration-blueprint-tmpl.md
    destination: .bmad-core/templates/agent-integration-blueprint-tmpl.md

  # Cross-Cutting Templates
  - source: templates/inter-service-communication-matrix-tmpl.md
    destination: .bmad-core/templates/inter-service-communication-matrix-tmpl.md
  - source: templates/micro-frontend-architecture-tmpl.md
    destination: .bmad-core/templates/micro-frontend-architecture-tmpl.md
  - source: templates/deployment-devops-strategy-tmpl.md
    destination: .bmad-core/templates/deployment-devops-strategy-tmpl.md
  - source: templates/agent-communication-protocol-tmpl.md
    destination: .bmad-core/templates/agent-communication-protocol-tmpl.md

  # Workflows
  - source: workflows/agentic-system-design.yml
    destination: .bmad-core/workflows/agentic-system-design.yml
  - source: workflows/microservice-development.yml
    destination: .bmad-core/workflows/microservice-development.yml
  - source: workflows/agent-integration.yml
    destination: .bmad-core/workflows/agent-integration.yml
  - source: workflows/micro-frontend-development.yml
    destination: .bmad-core/workflows/micro-frontend-development.yml

  # Tasks
  - source: tasks/design-service-boundaries.md
    destination: .bmad-core/tasks/design-service-boundaries.md
  - source: tasks/define-agent-roles.md
    destination: .bmad-core/tasks/define-agent-roles.md
  - source: tasks/create-communication-protocols.md
    destination: .bmad-core/tasks/create-communication-protocols.md
  - source: tasks/validate-system-architecture.md
    destination: .bmad-core/tasks/validate-system-architecture.md
  - source: tasks/design-agent-workflows.md
    destination: .bmad-core/tasks/design-agent-workflows.md

  # Checklists
  - source: checklists/system-architecture-checklist.md
    destination: .bmad-core/checklists/system-architecture-checklist.md
  - source: checklists/microservice-design-checklist.md
    destination: .bmad-core/checklists/microservice-design-checklist.md
  - source: checklists/agentic-ai-integration-checklist.md
    destination: .bmad-core/checklists/agentic-ai-integration-checklist.md
  - source: checklists/micro-frontend-checklist.md
    destination: .bmad-core/checklists/micro-frontend-checklist.md

  # Data & Knowledge Base
  - source: data/microservice-patterns.md
    destination: .bmad-core/data/microservice-patterns.md
  - source: data/agentic-ai-frameworks.md
    destination: .bmad-core/data/agentic-ai-frameworks.md
  - source: data/event-driven-patterns.md
    destination: .bmad-core/data/event-driven-patterns.md
  - source: data/micro-frontend-patterns.md
    destination: .bmad-core/data/micro-frontend-patterns.md

dependencies:
  core_agents:
    - architect
    - dev
    - pm
    - analyst
  expansion_agents:
    - system-architect
    - microservice-architect
    - agentic-ai-designer
    - micro-frontend-architect
    - service-orchestrator
    - agent-workflow-designer

compatibility:
  bmad_version: ">=4.0.0"
  node_version: ">=20.0.0"

installation_notes: |
  This expansion pack provides specialized agents and templates for building large-scale
  agentic AI-powered applications using microservices and micro-frontend architectures.
  
  Key Features:
  - System-level architecture design with embedded AI agents
  - Individual microservice design with agent integration
  - Micro-frontend architecture with cross-service coordination
  - Multi-agent workflow orchestration
  - Event-driven communication patterns
  - Comprehensive deployment and DevOps strategies
  
  Usage:
  1. Start with system-architect for overall system design
  2. Use microservice-architect for individual service design
  3. Apply agentic-ai-designer for AI agent integration
  4. Use micro-frontend-architect for frontend architecture
  5. Apply service-orchestrator for inter-service coordination
