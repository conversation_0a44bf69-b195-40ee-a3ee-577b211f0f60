#!/bin/bash

# BMAD Agentic Microservices Expansion Pack Build Script

echo "Building BMAD Agentic Microservices Expansion Pack..."

# Create dist directory
mkdir -p ../../dist/expansion-packs/bmad-agentic-microservices

# Copy all files to dist
cp -r . ../../dist/expansion-packs/bmad-agentic-microservices/

# Create bundled version for web UI
echo "Creating web bundle..."
cat > ../../dist/expansion-packs/bmad-agentic-microservices.txt << 'BUNDLE_EOF'
# BMAD Agentic Microservices Expansion Pack
# Complete framework for building large-scale distributed applications with embedded multi-agent systems

BUNDLE_EOF

# Append all agent files
for agent_file in agents/*.md; do
    echo "# === $(basename "$agent_file" .md) ===" >> ../../dist/expansion-packs/bmad-agentic-microservices.txt
    cat "$agent_file" >> ../../dist/expansion-packs/bmad-agentic-microservices.txt
    echo -e "\n\n" >> ../../dist/expansion-packs/bmad-agentic-microservices.txt
done

# Append key templates
for template_file in templates/system-*.md templates/agentic-*.md; do
    echo "# === TEMPLATE: $(basename "$template_file" .md) ===" >> ../../dist/expansion-packs/bmad-agentic-microservices.txt
    cat "$template_file" >> ../../dist/expansion-packs/bmad-agentic-microservices.txt
    echo -e "\n\n" >> ../../dist/expansion-packs/bmad-agentic-microservices.txt
done

# Append key workflows
echo "# === WORKFLOW: agentic-system-design ===" >> ../../dist/expansion-packs/bmad-agentic-microservices.txt
cat workflows/agentic-system-design.yml >> ../../dist/expansion-packs/bmad-agentic-microservices.txt

echo "Build complete! Expansion pack available at:"
echo "- Full pack: ../../dist/expansion-packs/bmad-agentic-microservices/"
echo "- Web bundle: ../../dist/expansion-packs/bmad-agentic-microservices.txt"
