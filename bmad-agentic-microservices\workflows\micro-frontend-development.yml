name: micro-frontend-development
description: Complete workflow for developing micro-frontends with embedded agentic AI systems
version: 1.0
category: frontend

# Workflow for developing micro-frontend applications with AI agent integration
# This workflow guides the development of micro-frontends from conception to deployment

phases:
  - name: frontend-planning
    description: Plan micro-frontend architecture and agent integration
    duration: 1-2 weeks
    agents:
      - micro-frontend-architect
      - agentic-ai-designer
    deliverables:
      - micro-frontend-architecture
      - agent-integration-strategy
      - technology-stack-decisions

  - name: design-system
    description: Create design system and shared components
    duration: 1-2 weeks
    agents:
      - micro-frontend-architect
      - designer
    deliverables:
      - design-system
      - shared-component-library
      - ui-agent-integration-patterns

  - name: shell-development
    description: Develop shell application and routing
    duration: 1-2 weeks
    agents:
      - dev
      - micro-frontend-architect
    deliverables:
      - shell-application
      - routing-configuration
      - agent-coordination-framework

  - name: micro-frontend-development
    description: Develop individual micro-frontend applications
    duration: 2-4 weeks
    agents:
      - dev
      - agentic-ai-designer
    deliverables:
      - micro-frontend-applications
      - embedded-agents
      - component-integration

  - name: integration-testing
    description: Test micro-frontend integration and agent coordination
    duration: 1 week
    agents:
      - qa
      - dev
    deliverables:
      - integration-test-results
      - agent-behavior-validation
      - performance-benchmarks

  - name: deployment-setup
    description: Set up deployment pipeline and infrastructure
    duration: 3-5 days
    agents:
      - service-orchestrator
      - dev
    deliverables:
      - deployment-pipeline
      - cdn-configuration
      - monitoring-setup

  - name: production-deployment
    description: Deploy micro-frontends to production
    duration: 1-2 days
    agents:
      - service-orchestrator
    deliverables:
      - production-deployment
      - monitoring-dashboards
      - operational-runbooks

tasks:
  frontend-planning:
    - analyze-user-requirements
    - design-micro-frontend-boundaries
    - plan-agent-integration-points
    - select-technology-stack
    - create-development-roadmap

  design-system:
    - create-design-tokens
    - build-component-library
    - design-agent-ui-patterns
    - establish-accessibility-standards
    - create-style-guidelines

  shell-development:
    - setup-shell-application
    - implement-routing-system
    - create-micro-frontend-loader
    - implement-agent-coordination
    - setup-shared-state-management

  micro-frontend-development:
    - setup-micro-frontend-projects
    - implement-core-features
    - integrate-embedded-agents
    - implement-inter-frontend-communication
    - create-component-tests

  integration-testing:
    - test-micro-frontend-composition
    - validate-agent-coordination
    - test-cross-frontend-workflows
    - perform-performance-testing
    - validate-accessibility-compliance

  deployment-setup:
    - configure-build-pipeline
    - setup-cdn-and-caching
    - configure-monitoring-and-logging
    - setup-feature-flag-system
    - create-deployment-scripts

  production-deployment:
    - deploy-to-staging
    - validate-staging-environment
    - deploy-to-production
    - validate-production-deployment
    - monitor-initial-performance

checkpoints:
  - name: architecture-approved
    phase: frontend-planning
    criteria:
      - micro-frontend-architecture-complete
      - technology-stack-approved
      - agent-integration-strategy-defined

  - name: design-system-ready
    phase: design-system
    criteria:
      - component-library-complete
      - design-tokens-defined
      - agent-ui-patterns-established

  - name: shell-operational
    phase: shell-development
    criteria:
      - shell-application-functional
      - routing-system-working
      - agent-coordination-implemented

  - name: micro-frontends-complete
    phase: micro-frontend-development
    criteria:
      - all-micro-frontends-implemented
      - agents-integrated-successfully
      - component-tests-passing

  - name: integration-validated
    phase: integration-testing
    criteria:
      - integration-tests-passing
      - agent-coordination-validated
      - performance-acceptable

  - name: deployment-ready
    phase: deployment-setup
    criteria:
      - pipeline-configured
      - infrastructure-ready
      - monitoring-operational

  - name: production-live
    phase: production-deployment
    criteria:
      - micro-frontends-deployed
      - monitoring-active
      - performance-acceptable

quality_gates:
  - name: code-quality
    description: Ensure code meets quality standards
    criteria:
      - code-coverage: ">= 80%"
      - static-analysis: "no-critical-issues"
      - security-scan: "no-high-vulnerabilities"
      - agent-integration-quality: "meets-standards"

  - name: performance
    description: Ensure micro-frontends meet performance requirements
    criteria:
      - bundle-size: "<= 500KB per micro-frontend"
      - load-time: "<= 3 seconds"
      - agent-response-time: "<= 200ms"
      - lighthouse-score: ">= 90"

  - name: accessibility
    description: Ensure accessibility compliance
    criteria:
      - wcag-compliance: "AA level"
      - screen-reader-compatibility: "validated"
      - keyboard-navigation: "fully-functional"
      - agent-accessibility: "compliant"

  - name: user-experience
    description: Ensure optimal user experience
    criteria:
      - cross-browser-compatibility: "validated"
      - responsive-design: "mobile-first"
      - agent-ux-integration: "seamless"
      - user-testing: "positive-feedback"