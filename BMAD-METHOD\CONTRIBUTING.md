# Contributing to this project

Thank you for considering contributing to this project! This document outlines the process for contributing and some guidelines to follow.

🆕 **New to GitHub or pull requests?** Check out our [beginner-friendly Pull Request Guide](docs/how-to-contribute-with-pull-requests.md) first!

Also note, we use the discussions feature in GitHub to have a community to discuss potential ideas, uses, additions and enhancements.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please read it before participating.

## How to Contribute

### Reporting Bugs

- Check if the bug has already been reported in the Issues section
- Include detailed steps to reproduce the bug
- Include any relevant logs or screenshots

### Suggesting Features

- Check if the feature has already been suggested in the Issues section, and consider using the discussions tab in GitHub also. Explain the feature in detail and why it would be valuable.

### Pull Request Process

Please only propose small granular commits! If its large or significant, please discuss in the discussions tab and open up an issue first. I do not want you to waste your time on a potentially very large PR to have it rejected because it is not aligned or deviates from other planned changes. Communicate and lets work together to build and improve this great community project!

1. Fork the repository
2. Create a new branch (`git checkout -b feature/your-feature-name`)
3. Make your changes
4. Run any tests or linting to ensure quality
5. Commit your changes with clear, descriptive messages following our commit message convention
6. Push to your branch (`git push origin feature/your-feature-name`)
7. Open a Pull Request against the main branch

## Commit Message Convention

PRs with a wall of AI Generated marketing hype that is unclear in what is being proposed will be closed and rejected. Your best change to contribute is with a small clear PR description explaining, what is the issue being solved or gap in the system being filled. Also explain how it leads to the core guiding principles of the project.

## Code Style

- Follow the existing code style and conventions
- Write clear comments for complex logic

## License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project.
