installation-options:
  full:
    name: Complete BMAD Core
    description: Copy the entire .bmad-core folder with all agents, templates, and tools
    action: copy-folder
    source: bmad-core
  single-agent:
    name: Single Agent
    description: Select and install a single agent with its dependencies
    action: copy-agent
agent-dependencies:
  core-files:
    - bmad-core/utils/template-format.md
  dev:
    - bmad-core/templates/story-tmpl.md
    - bmad-core/checklists/story-dod-checklist.md
  pm:
    - bmad-core/templates/prd-tmpl.md
    - bmad-core/templates/brownfield-prd-tmpl.md
    - bmad-core/checklists/pm-checklist.md
    - bmad-core/checklists/change-checklist.md
    - bmad-core/tasks/advanced-elicitation.md
    - bmad-core/tasks/create-doc.md
    - bmad-core/tasks/correct-course.md
    - bmad-core/tasks/create-deep-research-prompt.md
    - bmad-core/tasks/brownfield-create-epic.md
    - bmad-core/tasks/brownfield-create-story.md
    - bmad-core/tasks/execute-checklist.md
    - bmad-core/tasks/shard-doc.md
  architect:
    - bmad-core/templates/architecture-tmpl.md
    - bmad-core/checklists/architect-checklist.md
  sm:
    - bmad-core/templates/story-tmpl.md
    - bmad-core/checklists/story-draft-checklist.md
    - bmad-core/workflows/*.yml
  po:
    - bmad-core/checklists/po-master-checklist.md
    - bmad-core/templates/acceptance-criteria-tmpl.md
  analyst:
    - bmad-core/templates/prd-tmpl.md
    - bmad-core/tasks/advanced-elicitation.md
  qa:
    - bmad-core/checklists/story-dod-checklist.md
    - bmad-core/templates/test-plan-tmpl.md
  ux-expert:
    - bmad-core/templates/ux-tmpl.md
  bmad-master:
    - bmad-core/templates/*.md
    - bmad-core/tasks/*.md
    - bmad-core/schemas/*.yml
  bmad-orchestrator:
    - bmad-core/agent-teams/*.yml
    - bmad-core/workflows/*.yml
ide-configurations:
  cursor:
    name: Cursor
    rule-dir: .cursor/rules/
    format: multi-file
    command-suffix: .mdc
    instructions: |
      # To use BMAD agents in Cursor:
      # 1. Press Ctrl+L (Cmd+L on Mac) to open the chat
      # 2. Type @agent-name (e.g., "@dev", "@pm", "@architect")
      # 3. The agent will adopt that persona for the conversation
  claude-code:
    name: Claude Code
    rule-dir: .claude/commands/
    format: multi-file
    command-suffix: .md
    instructions: |
      # To use BMAD agents in Claude Code:
      # 1. Type /agent-name (e.g., "/dev", "/pm", "/architect")
      # 2. Claude will switch to that agent's persona
  windsurf:
    name: Windsurf
    rule-dir: .windsurf/rules/
    format: multi-file
    command-suffix: .md
    instructions: |
      # To use BMAD agents in Windsurf:
      # 1. Type @agent-name (e.g., "@dev", "@pm")
      # 2. Windsurf will adopt that agent's persona
  roo:
    name: Roo Code
    format: custom-modes
    file: .roomodes
    instructions: |
      # To use BMAD agents in Roo Code:
      # 1. Open the mode selector (usually in the status bar)
      # 2. Select any bmad-{agent} mode (e.g., "bmad-dev", "bmad-pm")
      # 3. The AI will adopt that agent's full personality and capabilities
  cline:
    name: Cline
    format: unknown
    instructions: |
      # Cline configuration coming soon
      # Manual setup: Copy IDE agent files to your Cline configuration
available-agents:
  - id: analyst
    name: Business Analyst
    file: bmad-core/agents/analyst.md
    description: Requirements gathering and analysis
  - id: pm
    name: Product Manager
    file: bmad-core/agents/pm.md
    description: Product strategy and roadmap planning
  - id: architect
    name: Solution Architect
    file: bmad-core/agents/architect.md
    description: Technical design and architecture
  - id: po
    name: Product Owner
    file: bmad-core/agents/po.md
    description: Backlog management and prioritization
  - id: sm
    name: Scrum Master
    file: bmad-core/agents/sm.md
    description: Agile process and story creation
  - id: dev
    name: Developer
    file: bmad-core/agents/dev.md
    description: Code implementation and testing
  - id: qa
    name: QA Engineer
    file: bmad-core/agents/qa.md
    description: Quality assurance and testing
  - id: ux-expert
    name: UX Expert
    file: bmad-core/agents/ux-expert.md
    description: User experience design
  - id: bmad-master
    name: BMAD Master
    file: bmad-core/agents/bmad-master.md
    description: BMAD framework expert and guide
  - id: bmad-orchestrator
    name: BMAD Orchestrator
    file: bmad-core/agents/bmad-orchestrator.md
    description: Multi-agent workflow coordinator
