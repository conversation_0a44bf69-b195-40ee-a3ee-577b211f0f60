# BMAD Agentic Microservices Installation Guide

## Overview
This guide provides step-by-step instructions for installing and configuring the consolidated BMAD Agentic Microservices extension pack.

## Prerequisites

### System Requirements
- **Node.js**: v20.0.0 or higher
- **BMAD-METHOD**: v4.0.0 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 500MB free space

### Knowledge Requirements
- Basic understanding of microservices architecture
- Familiarity with BMAD-METHOD framework
- Basic knowledge of AI/ML concepts
- Understanding of distributed systems

## Installation Methods

### Method 1: NPX Installation (Recommended)

#### Step 1: Verify Prerequisites
```bash
# Check Node.js version
node --version

# Check BMAD-METHOD installation
npx bmad-method --version
```

#### Step 2: Install Extension Pack
```bash
# Install the consolidated extension pack
npx bmad-method install-expansion bmad-agentic-microservices

# Verify installation
npx bmad-method list-expansions
```

#### Step 3: Validate Installation
```bash
# Check that all agents are available
npx bmad-method list-agents | grep -E "(system-architect|microservice-architect|agentic-ai-designer)"

# Validate templates are installed
ls .bmad-core/templates/ | grep -E "(system-|microservice-|agentic-)"
```

### Method 2: Manual Installation

#### Step 1: Download Extension Pack
```bash
# Clone or download the consolidated extension pack
git clone <repository-url> bmad-agentic-microservices
cd bmad-agentic-microservices
```

#### Step 2: Copy to BMAD Installation
```bash
# Copy to your project's BMAD installation
cp -r bmad-agentic-microservices /path/to/your/project/.bmad-core/expansion-packs/

# Or for Windows
xcopy bmad-agentic-microservices C:\path\to\your\project\.bmad-core\expansion-packs\ /E /I
```

#### Step 3: Update BMAD Configuration
Add the extension pack to your BMAD configuration file:
```yaml
# .bmad-core/config.yml
expansion_packs:
  - bmad-agentic-microservices
```

## Configuration

### Basic Configuration
The extension pack works out-of-the-box with default settings. For custom configurations:

```yaml
# .bmad-core/expansion-packs/bmad-agentic-microservices/config.yml
agents:
  system-architect:
    enabled: true
    model: "gpt-4"
  microservice-architect:
    enabled: true
    model: "gpt-4"
  agentic-ai-designer:
    enabled: true
    model: "gpt-4"
```

### IDE Integration
Configure your IDE to use the new agents:

#### Cursor IDE
```bash
# Agents will be available as @system-architect, @microservice-architect, etc.
@system-architect *help
```

#### Claude Code
```bash
# Agents will be available as /system-architect, /microservice-architect, etc.
/system-architect *help
```

#### Windsurf
```bash
# Agents will be available as @system-architect, @microservice-architect, etc.
@system-architect *help
```

## Verification

### Test Agent Functionality
```bash
# Test system architect agent
@system-architect *help

# Test microservice architect agent
@microservice-architect *help

# Test agentic AI designer agent
@agentic-ai-designer *help
```

### Verify Templates
```bash
# List available templates
@system-architect *create-doc

# Should show templates like:
# - system-project-brief-tmpl
# - system-prd-tmpl
# - system-architecture-tmpl
# - agentic-system-design-tmpl
```

### Check Workflows
```bash
# Verify workflows are available
ls .bmad-core/workflows/ | grep -E "(agentic-system-design|microservice-development|agent-integration)"
```

## Quick Start

### 1. Create Your First System Design
```bash
# Start with system architecture
@system-architect *create-doc system-project-brief-tmpl

# Follow up with system PRD
@system-architect *create-doc system-prd-tmpl

# Design the agentic system
@agentic-ai-designer *create-doc agentic-system-design-tmpl
```

### 2. Design Individual Microservices
```bash
# Create microservice brief
@microservice-architect *create-doc microservice-brief-tmpl

# Create microservice PRD
@microservice-architect *create-doc microservice-prd-tmpl

# Design agent integration
@agentic-ai-designer *create-doc agent-integration-blueprint-tmpl
```

### 3. Implement and Deploy
```bash
# Use development workflow
@service-orchestrator *execute-workflow microservice-development

# Integrate agents
@agentic-ai-designer *execute-workflow agent-integration
```

## Troubleshooting

### Common Issues

#### Issue: Agents Not Found
**Symptoms**: `@system-architect` command not recognized
**Solution**:
```bash
# Reinstall extension pack
npx bmad-method install-expansion bmad-agentic-microservices --force

# Verify installation
npx bmad-method list-agents
```

#### Issue: Templates Not Available
**Symptoms**: Templates not showing in `*create-doc` command
**Solution**:
```bash
# Check template directory
ls .bmad-core/templates/ | grep system-

# If missing, reinstall
npx bmad-method install-expansion bmad-agentic-microservices --force
```

#### Issue: Permission Errors
**Symptoms**: Permission denied during installation
**Solution**:
```bash
# On Unix systems, use sudo if needed
sudo npx bmad-method install-expansion bmad-agentic-microservices

# Or change ownership
sudo chown -R $USER:$USER .bmad-core/
```

### Getting Help

#### Documentation
- [BMAD-METHOD Documentation](../../docs/)
- [Extension Pack README](./README.md)
- [Consolidation Report](./CONSOLIDATION_REPORT.md)

#### Community Support
- [Discord Community](https://discord.gg/g6ypHytrCB)
- [GitHub Discussions](https://github.com/bmadcode/bmad-method/discussions)
- [Issue Tracker](https://github.com/bmadcode/bmad-method/issues)

#### Professional Support
For enterprise support and consulting:
- Email: <EMAIL>
- Website: https://bmadcode.com

## Migration from Previous Versions

### From bmad-agentic-microservices_1 or _2
If you were using either of the original implementations:

1. **Backup Existing Work**:
   ```bash
   cp -r .bmad-core/expansion-packs/bmad-agentic-microservices_1 ./backup/
   ```

2. **Remove Old Installation**:
   ```bash
   rm -rf .bmad-core/expansion-packs/bmad-agentic-microservices_1
   rm -rf .bmad-core/expansion-packs/bmad-agentic-microservices_2
   ```

3. **Install Consolidated Version**:
   ```bash
   npx bmad-method install-expansion bmad-agentic-microservices
   ```

4. **Migrate Custom Configurations**:
   - Review your backup configurations
   - Apply relevant customizations to the new installation
   - Test functionality with new templates and workflows

### Benefits of Migration
- **Complete Functionality**: Access to all 19 previously missing files
- **Enhanced Templates**: Comprehensive templates for all development phases
- **Improved Workflows**: Complete automation workflows
- **Better Documentation**: Comprehensive guides and examples
- **Unified Experience**: Single, consistent extension pack

## Advanced Configuration

### Custom Agent Models
```yaml
# .bmad-core/expansion-packs/bmad-agentic-microservices/config.yml
agents:
  system-architect:
    model: "claude-3-opus"
    temperature: 0.7
    max_tokens: 4000
  microservice-architect:
    model: "gpt-4-turbo"
    temperature: 0.5
    max_tokens: 3000
```

### Custom Templates
You can create custom templates by:
1. Copying existing templates
2. Modifying for your specific needs
3. Placing in the templates directory
4. Updating agent dependencies

### Integration with CI/CD
```yaml
# .github/workflows/bmad-agentic.yml
name: BMAD Agentic Microservices
on: [push, pull_request]
jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '20'
      - run: npx bmad-method install-expansion bmad-agentic-microservices
      - run: npx bmad-method validate
```

## Next Steps

After successful installation:

1. **Explore the Documentation**: Read through all available templates and workflows
2. **Start Small**: Begin with a simple microservice design
3. **Experiment with Agents**: Try different agent combinations
4. **Join the Community**: Connect with other users for tips and best practices
5. **Provide Feedback**: Help improve the extension pack with your experiences

## Support and Maintenance

### Updates
The extension pack is regularly updated. To get the latest version:
```bash
npx bmad-method update-expansion bmad-agentic-microservices
```

### Backup and Recovery
Always backup your work before major updates:
```bash
# Backup your entire BMAD installation
tar -czf bmad-backup-$(date +%Y%m%d).tar.gz .bmad-core/
```

### Performance Optimization
For large projects, consider:
- Enabling agent caching
- Optimizing template loading
- Using selective agent activation
- Implementing custom workflows for your specific use cases
