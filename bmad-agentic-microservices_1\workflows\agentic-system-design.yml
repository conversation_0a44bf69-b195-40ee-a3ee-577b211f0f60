workflow:
  id: agentic-system-design
  name: Agentic AI-Powered Microservices System Design
  description: >-
    Complete workflow for designing large-scale distributed systems with microservices,
    micro-frontends, and embedded agentic AI systems. Guides from initial concept
    through detailed system architecture and implementation planning.
  type: greenfield
  project_types:
    - distributed-system
    - microservices
    - agentic-ai
    - enterprise-scale
    - multi-team

  # System-Level Design Sequence
  system_design_sequence:
    - agent: analyst
      creates: system-project-brief.md
      optional_steps:
        - market_research
        - competitive_analysis
        - stakeholder_interviews
      notes: "Create comprehensive system-level project brief covering business vision, scale requirements, and agentic AI strategy. SAVE OUTPUT: Copy to docs/system-project-brief.md"

    - agent: system-architect
      creates: system-prd.md
      requires: system-project-brief.md
      optional_steps:
        - domain_analysis
        - service_boundary_research
      notes: "Create system-level PRD covering all microservices, cross-cutting concerns, and system-wide agentic AI requirements. SAVE OUTPUT: Copy to docs/system-prd.md"

    - agent: agentic-ai-designer
      creates: agentic-system-design.md
      requires: system-prd.md
      optional_steps:
        - ai_framework_research
        - agent_workflow_analysis
      notes: "Design comprehensive multi-agent system architecture, agent hierarchies, and cross-service coordination. SAVE OUTPUT: Copy to docs/agentic-system-design.md"

    - agent: system-architect
      creates: system-architecture.md
      requires:
        - system-prd.md
        - agentic-system-design.md
      optional_steps:
        - technology_stack_research
        - infrastructure_planning
      notes: "Create complete system architecture covering microservices, micro-frontends, and infrastructure. SAVE OUTPUT: Copy to docs/system-architecture.md"

    - agent: micro-frontend-architect
      creates: micro-frontend-architecture.md
      requires: system-architecture.md
      optional_steps:
        - frontend_framework_research
        - design_system_planning
      notes: "Design micro-frontend architecture with module federation and shared design system. SAVE OUTPUT: Copy to docs/micro-frontend-architecture.md"

    - agent: service-orchestrator
      creates: inter-service-communication-matrix.md
      requires: system-architecture.md
      optional_steps:
        - communication_pattern_research
        - event_schema_design
      notes: "Design comprehensive inter-service communication patterns and protocols. SAVE OUTPUT: Copy to docs/inter-service-communication-matrix.md"

  # Individual Service Design Sequence
  service_design_sequence:
    - agent: microservice-architect
      creates: microservice-brief-{service-name}.md
      requires: system-architecture.md
      notes: "For each identified microservice, create detailed service brief. Repeat for each service. SAVE OUTPUT: Copy to docs/services/{service-name}-brief.md"

    - agent: microservice-architect
      creates: microservice-prd-{service-name}.md
      requires: microservice-brief-{service-name}.md
      notes: "Create detailed PRD for each microservice including API contracts and data models. SAVE OUTPUT: Copy to docs/services/{service-name}-prd.md"

    - agent: agentic-ai-designer
      creates: agent-integration-blueprint-{service-name}.md
      requires: microservice-prd-{service-name}.md
      notes: "Design specific AI agent integration for each service. SAVE OUTPUT: Copy to docs/services/{service-name}-agent-blueprint.md"

    - agent: microservice-architect
      creates: microservice-architecture-{service-name}.md
      requires:
        - microservice-prd-{service-name}.md
        - agent-integration-blueprint-{service-name}.md
      notes: "Create detailed technical architecture for each microservice. SAVE OUTPUT: Copy to docs/services/{service-name}-architecture.md"

  # Cross-Cutting Design Sequence
  cross_cutting_sequence:
    - agent: agent-workflow-designer
      creates: agent-communication-protocols.md
      requires: agentic-system-design.md
      notes: "Design detailed agent communication protocols and workflows. SAVE OUTPUT: Copy to docs/agent-communication-protocols.md"

    - agent: service-orchestrator
      creates: deployment-devops-strategy.md
      requires: system-architecture.md
      notes: "Create comprehensive deployment and DevOps strategy for distributed system. SAVE OUTPUT: Copy to docs/deployment-devops-strategy.md"

  # Validation & Quality Assurance
  validation_sequence:
    - agent: system-architect
      executes: validate-system-architecture
      requires: system-architecture.md
      notes: "Run comprehensive system architecture validation checklist"

    - agent: agentic-ai-designer
      executes: validate-agentic-integration
      requires: agentic-system-design.md
      notes: "Validate AI agent integration design and ethical considerations"

    - agent: micro-frontend-architect
      executes: validate-micro-frontend-design
      requires: micro-frontend-architecture.md
      notes: "Validate micro-frontend architecture and design system"

  # Implementation Planning
  implementation_planning:
    - agent: pm
      creates: implementation-roadmap.md
      requires:
        - system-architecture.md
        - All service architectures
      notes: "Create detailed implementation roadmap with phases and milestones. SAVE OUTPUT: Copy to docs/implementation-roadmap.md"

    - agent: dev
      creates: development-setup-guide.md
      requires: system-architecture.md
      notes: "Create development environment setup guide for distributed development. SAVE OUTPUT: Copy to docs/development-setup-guide.md"

workflow_variations:
  simple_system:
    description: "For systems with 3-5 microservices"
    skip_steps:
      - competitive_analysis
      - technology_stack_research
    combine_steps:
      - system_prd_and_architecture

  complex_enterprise:
    description: "For large enterprise systems with 10+ microservices"
    additional_steps:
      - security_architecture_review
      - compliance_validation
      - performance_modeling
    additional_agents:
      - security-architect
      - performance-engineer

  ai_first_system:
    description: "For systems where AI agents are the primary focus"
    emphasis:
      - agentic_ai_design
      - agent_workflow_design
      - ai_ethics_review
    additional_validation:
      - ai_bias_testing
      - explainability_review

success_criteria:
  documentation_completeness:
    - System-level architecture documents
    - Individual service specifications
    - Agent integration blueprints
    - Communication protocols
    - Deployment strategies

  architecture_quality:
    - Clear service boundaries
    - Well-defined agent roles
    - Scalable communication patterns
    - Comprehensive error handling
    - Security considerations

  team_readiness:
    - Clear team responsibilities
    - Development environment setup
    - Implementation roadmap
    - Quality assurance processes

best_practices:
  - Start with business domain analysis before technical design
  - Design agent integration from the beginning, not as an afterthought
  - Plan for independent team development and deployment
  - Consider data consistency and communication patterns early
  - Design comprehensive monitoring and observability from the start
  - Plan for system evolution and scaling
  - Include security and compliance considerations throughout
  - Validate designs with stakeholders at each major milestone

common_pitfalls:
  - Designing too many small services (nano-services)
  - Not considering data consistency across service boundaries
  - Underestimating the complexity of distributed debugging
  - Not planning for agent coordination and communication
  - Ignoring the operational complexity of distributed systems
  - Not considering team Conway's Law implications
  - Insufficient planning for cross-cutting concerns
